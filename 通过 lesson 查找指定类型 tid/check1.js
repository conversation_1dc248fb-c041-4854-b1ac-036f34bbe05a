/*
 * @FilePath     : /check1.js
 * <AUTHOR> wanghuan
 * @description  : 
 * @warn         : 
 */
// 通过 lesson 导出所有tid 的 update 打点，再计算出指定的 category 的 成功





// 读取 csv 地址为  /Users/<USER>/Downloads/查询当前所有update_2_0801.csv  



const categorys = [1156,1010,1176];

// 第二个 API：根据 tid 获取数据
async function fetchByTid(tid) {
    return new Promise((resolve, reject) => {
        const options = {
            hostname: 'tikuproxy.zuoyebang.cc',
            path: `/tikuproxy/api/v1/getbytid?tid=${tid}`,
            method: 'GET',
            headers: {
                "cookie": "sensorsdata2015session=%7B%7D; ZYBIPSCAS=IPS_06b5d1590d15f446a83ccaed407610461753928756; ZYBIPSUN=77616e676875616e3133"
            }
        }

        const req = https.request(options, (res) => {
            let data = '';

            res.on('data', (chunk) => {
                data += chunk;
            });

            res.on('end', () => {
                try {
                    if (!data || data.trim() === '') {
                        console.log(`    tid ${tid} 返回空响应`);
                        resolve(null);
                        return;
                    }
                    const jsonData = JSON.parse(data);
                    console.log(`    tid ${tid} API 响应状态: ${res.statusCode}`);
                    resolve(jsonData);
                } catch (error) {
                    console.error(`解析 tid ${tid} 的响应数据失败:`, error);
                    console.error(`响应内容: ${data.substring(0, 200)}...`);
                    resolve(null);
                }
            });
        });

        req.on('error', (error) => {
            console.error(`获取 tid ${tid} 数据失败:`, error);
            resolve(null);
        });

        req.end();
    });
}