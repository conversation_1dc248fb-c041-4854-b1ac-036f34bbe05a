/*
 * @FilePath     : /read_csv.js
 * <AUTHOR> wanghuan
 * @description  : 读取 CSV 文件
 * @warn         : 
 */

import fs from 'fs';
import path from 'path';

// CSV 文件路径
const csvFilePath = '/Users/<USER>/Downloads/查询当前所有update_2_0801.csv';

// 简单的 CSV 解析函数
function parseCSV(csvText) {
    const lines = csvText.split('\n');
    const result = [];
    
    for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();
        if (line) {
            // 简单的 CSV 解析，处理逗号分隔
            const columns = line.split(',').map(col => col.trim().replace(/^"|"$/g, ''));
            result.push(columns);
        }
    }
    
    return result;
}

// 读取和解析 CSV 文件
async function readCSV() {
    try {
        console.log(`正在读取 CSV 文件: ${csvFilePath}`);
        
        // 检查文件是否存在
        if (!fs.existsSync(csvFilePath)) {
            console.error('文件不存在:', csvFilePath);
            return;
        }
        
        // 读取文件
        const csvContent = fs.readFileSync(csvFilePath, 'utf8');
        console.log(`文件大小: ${csvContent.length} 字符`);
        
        // 解析 CSV
        const csvData = parseCSV(csvContent);
        console.log(`解析完成，共 ${csvData.length} 行数据`);
        
        // 显示前几行数据
        console.log('\n=== CSV 文件内容预览 ===');
        const previewRows = Math.min(10, csvData.length);
        
        for (let i = 0; i < previewRows; i++) {
            console.log(`第 ${i + 1} 行:`, csvData[i]);
        }
        
        if (csvData.length > 10) {
            console.log(`\n... 还有 ${csvData.length - 10} 行数据`);
        }
        
        // 如果有表头，显示列信息
        if (csvData.length > 0) {
            console.log('\n=== 列信息 ===');
            const headers = csvData[0];
            headers.forEach((header, index) => {
                console.log(`列 ${index + 1}: ${header}`);
            });
        }
        
        return csvData;
        
    } catch (error) {
        console.error('读取 CSV 文件时发生错误:', error);
    }
}

// 执行读取
let csvData = readCSV();




/*
 * @FilePath     : /check1.js
 * <AUTHOR> wanghuan
 * @description  : 
 * @warn         : 
 */
// 通过 lesson 导出所有tid 的 update 打点，再计算出指定的 category 的 


// 第二个 API：根据 tid 获取数据
async function fetchByTid(tid) {
    return new Promise((resolve, reject) => {
        const options = {
            hostname: 'tikuproxy.zuoyebang.cc',
            path: `/tikuproxy/api/v1/getbytid?tid=${tid}`,
            method: 'GET',
            headers: {
                "cookie": "sensorsdata2015session=%7B%7D; ZYBIPSCAS=IPS_06b5d1590d15f446a83ccaed407610461753928756; ZYBIPSUN=77616e676875616e3133"
            }
        }

        const req = https.request(options, (res) => {
            let data = '';

            res.on('data', (chunk) => {
                data += chunk;
            });

            res.on('end', () => {
                try {
                    if (!data || data.trim() === '') {
                        console.log(`    tid ${tid} 返回空响应`);
                        resolve(null);
                        return;
                    }
                    const jsonData = JSON.parse(data);
                    console.log(`    tid ${tid} API 响应状态: ${res.statusCode}`);
                    resolve(jsonData);
                } catch (error) {
                    console.error(`解析 tid ${tid} 的响应数据失败:`, error);
                    console.error(`响应内容: ${data.substring(0, 200)}...`);
                    resolve(null);
                }
            });
        });

        req.on('error', (error) => {
            console.error(`获取 tid ${tid} 数据失败:`, error);
            resolve(null);
        });

        req.end();
    });
}