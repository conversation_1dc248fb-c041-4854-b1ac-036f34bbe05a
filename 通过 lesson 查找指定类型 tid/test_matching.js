/*
 * @FilePath     : /test_matching.js
 * <AUTHOR> wanghuan
 * @description  : 测试优化后的匹配逻辑（只运行步骤1和2）
 * @warn         : 
 */

import fs from 'fs';

// CSV 文件路径
const csvFilePath = '/Users/<USER>/Downloads/查询当前所有update_2_0801.csv';

// 简单的 CSV 解析函数
function parseCSV(csvText) {
    const lines = csvText.split('\n');
    const result = [];
    
    for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();
        if (line) {
            // 简单的 CSV 解析，处理逗号分隔
            const columns = line.split(',').map(col => col.trim().replace(/^"|"$/g, ''));
            result.push(columns);
        }
    }
    
    return result;
}

// 测试匹配逻辑
async function testMatchingLogic() {
    try {
        console.log('=== 测试优化后的匹配逻辑 ===');
        console.log(`正在读取 CSV 文件: ${csvFilePath}`);
        
        // 检查文件是否存在
        if (!fs.existsSync(csvFilePath)) {
            console.error('文件不存在:', csvFilePath);
            return;
        }
        
        // 读取文件
        const csvContent = fs.readFileSync(csvFilePath, 'utf8');
        console.log(`文件大小: ${csvContent.length} 字符`);
        
        // 解析 CSV
        const csvData = parseCSV(csvContent);
        console.log(`解析完成，共 ${csvData.length} 行数据`);
        
        // 跳过表头，从第二行开始处理
        const dataRows = csvData.slice(1);
        console.log(`数据行数: ${dataRows.length}`);
        
        // 步骤1: 数据分组
        console.log('\n=== 步骤1: 数据分组 ===');
        const fmg010Group = [];
        const fmg011Group = [];
        
        dataRows.forEach((row, index) => {
            const dotName = row[5]; // 第6列，索引为5
            if (dotName === 'FMG_010') {
                fmg010Group.push(row);
            } else if (dotName === 'FMG_011') {
                fmg011Group.push(row);
            }
        });
        
        console.log(`FMG_010组: ${fmg010Group.length} 条记录`);
        console.log(`FMG_011组: ${fmg011Group.length} 条记录`);
        
        // 步骤2: 数据匹配（优化后的逻辑）
        console.log('\n=== 步骤2: 数据匹配（优化后的逻辑）===');
        const matchedRecords = [];
        const unmatchedRecords = [];
        const seenCombinations = new Set(); // 用于去重的集合
        let duplicateCount = 0; // 重复记录计数
        
        for (const fmg010Record of fmg010Group) {
            const lessonId = fmg010Record[3]; // 第4列，索引为3
            const tid = fmg010Record[6]; // 第7列，索引为6
            const logTimestamp = fmg010Record[7]; // 第8列，索引为7 (log_timestamp)
            const combinationKey = `${lessonId}_${tid}_${logTimestamp}`; // 创建唯一标识符，包含时间戳
            
            // 在FMG_011组中查找匹配的记录
            const matchedFmg011 = fmg011Group.find(fmg011Record => 
                fmg011Record[3] === lessonId && fmg011Record[6] === tid
            );
            
            if (matchedFmg011) {
                // 检查是否已存在相同的 lessonId、tid 和 log_timestamp 组合
                if (seenCombinations.has(combinationKey)) {
                    duplicateCount++;
                    console.log(`  发现重复记录: lessonId=${lessonId}, tid=${tid}, log_timestamp=${logTimestamp}`);
                } else {
                    // 添加到匹配记录数组
                    matchedRecords.push({
                        lessonId,
                        tid,
                        logTimestamp,
                        fmg010Record,
                        fmg011Record: matchedFmg011
                    });
                    seenCombinations.add(combinationKey);
                }
            } else {
                // 记录不匹配的项
                unmatchedRecords.push({
                    lessonId,
                    tid,
                    logTimestamp,
                    fmg010Record
                });
            }
        }
        
        // 输出统计信息
        console.log('\n--- 匹配统计信息 ---');
        console.log(`原始 FMG_010 记录总数: ${fmg010Group.length}`);
        console.log(`找到匹配的记录数（去重后）: ${matchedRecords.length}`);
        console.log(`重复匹配的记录数: ${duplicateCount}`);
        console.log(`不匹配的记录数: ${unmatchedRecords.length}`);
        console.log(`匹配率: ${((matchedRecords.length / fmg010Group.length) * 100).toFixed(2)}%`);
        
        // 显示一些不匹配记录的示例
        if (unmatchedRecords.length > 0) {
            console.log('\n--- 不匹配记录示例（前5条）---');
            const sampleUnmatched = unmatchedRecords.slice(0, 5);
            sampleUnmatched.forEach((record, index) => {
                console.log(`  ${index + 1}. lessonId: ${record.lessonId}, tid: ${record.tid}, log_timestamp: ${record.logTimestamp}`);
            });
            if (unmatchedRecords.length > 5) {
                console.log(`  ... 还有 ${unmatchedRecords.length - 5} 条不匹配记录`);
            }
        }
        
        console.log('\n--- 去重逻辑说明 ---');
        console.log('去重标准: lessonId + tid + log_timestamp 的组合必须唯一');
        console.log('如果 lessonId 和 tid 相同，但 log_timestamp 不同，则不视为重复记录');
        
        // 显示一些匹配记录的示例
        if (matchedRecords.length > 0) {
            console.log('\n--- 匹配记录示例（前5条）---');
            const sampleMatched = matchedRecords.slice(0, 5);
            sampleMatched.forEach((record, index) => {
                console.log(`  ${index + 1}. lessonId: ${record.lessonId}, tid: ${record.tid}, log_timestamp: ${record.logTimestamp}`);
            });
            if (matchedRecords.length > 5) {
                console.log(`  ... 还有 ${matchedRecords.length - 5} 条匹配记录`);
            }
        }
        
        console.log('\n✅ 匹配逻辑测试完成！');
        
    } catch (error) {
        console.error('测试匹配逻辑时发生错误:', error);
    }
}

// 执行测试
testMatchingLogic();
