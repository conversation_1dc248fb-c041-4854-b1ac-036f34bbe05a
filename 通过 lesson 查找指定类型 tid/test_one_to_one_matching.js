/*
 * @FilePath     : /test_one_to_one_matching.js
 * <AUTHOR> wanghuan
 * @description  : 测试优化后的一对一匹配逻辑
 * @warn         : 
 */

import fs from 'fs';

// CSV 文件路径
const csvFilePath = '/Users/<USER>/Downloads/查询当前所有update_2_0801.csv';

// 简单的 CSV 解析函数
function parseCSV(csvText) {
    const lines = csvText.split('\n');
    const result = [];
    
    for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();
        if (line) {
            // 简单的 CSV 解析，处理逗号分隔
            const columns = line.split(',').map(col => col.trim().replace(/^"|"$/g, ''));
            result.push(columns);
        }
    }
    
    return result;
}

// 测试一对一匹配逻辑
async function testOneToOneMatching() {
    try {
        console.log('=== 测试一对一匹配逻辑 ===');
        console.log(`正在读取 CSV 文件: ${csvFilePath}`);
        
        // 检查文件是否存在
        if (!fs.existsSync(csvFilePath)) {
            console.error('文件不存在:', csvFilePath);
            return;
        }
        
        // 读取文件
        const csvContent = fs.readFileSync(csvFilePath, 'utf8');
        console.log(`文件大小: ${csvContent.length} 字符`);
        
        // 解析 CSV
        const csvData = parseCSV(csvContent);
        console.log(`解析完成，共 ${csvData.length} 行数据`);
        
        // 跳过表头，从第二行开始处理
        const dataRows = csvData.slice(1);
        console.log(`数据行数: ${dataRows.length}`);
        
        // 步骤1: 数据分组
        console.log('\n=== 步骤1: 数据分组 ===');
        const fmg010Group = [];
        const fmg011Group = [];
        
        dataRows.forEach((row, index) => {
            const dotName = row[5]; // 第6列，索引为5
            if (dotName === 'FMG_010') {
                fmg010Group.push(row);
            } else if (dotName === 'FMG_011') {
                fmg011Group.push(row);
            }
        });
        
        console.log(`FMG_010组: ${fmg010Group.length} 条记录`);
        console.log(`FMG_011组: ${fmg011Group.length} 条记录`);
        
        // 步骤2: 数据匹配（一对一匹配机制）
        console.log('\n=== 步骤2: 数据匹配（一对一匹配机制）===');
        const matchedRecords = [];
        const unmatchedFmg010Records = []; // FMG_010中无法找到匹配的记录
        const seenCombinations = new Set(); // 用于去重的集合
        let duplicateCount = 0; // 重复记录计数
        
        // 创建FMG_011组的副本用于匹配操作，避免修改原数组
        const availableFmg011Records = [...fmg011Group];
        
        console.log('\n--- 开始一对一匹配 ---');
        console.log(`FMG_010组记录数: ${fmg010Group.length}`);
        console.log(`FMG_011组记录数: ${fmg011Group.length}`);
        console.log(`理论最大匹配数: ${Math.min(fmg010Group.length, fmg011Group.length)}`);
        
        for (const fmg010Record of fmg010Group) {
            const matchField1 = fmg010Record[4]; // 第5列，索引为4
            const matchField2 = fmg010Record[10]; // 第11列，索引为10
            const lessonId = fmg010Record[3]; // 第4列，索引为3 (保留用于显示)
            const tid = fmg010Record[6]; // 第7列，索引为6 (保留用于显示)
            const logTimestamp = fmg010Record[7]; // 第8列，索引为7 (log_timestamp)
            const combinationKey = `${matchField1}_${matchField2}_${logTimestamp}`; // 使用新的匹配字段创建唯一标识符

            console.log(`  匹配字段: [4]=${matchField1}, [10]=${matchField2}, lessonId=${lessonId}, tid=${tid}`);

            // 在可用的FMG_011记录中查找匹配的记录（使用索引4和10进行匹配）
            const matchedIndex = availableFmg011Records.findIndex(fmg011Record =>
                fmg011Record[4] === matchField1 && fmg011Record[10] === matchField2
            );
            
            if (matchedIndex !== -1) {
                const matchedFmg011 = availableFmg011Records[matchedIndex];
                
                // 检查是否已存在相同的 lessonId、tid 和 log_timestamp 组合
                if (seenCombinations.has(combinationKey)) {
                    duplicateCount++;
                    console.log(`  发现重复记录: lessonId=${lessonId}, tid=${tid}, log_timestamp=${logTimestamp}`);
                    // 重复记录也算作无法匹配
                    unmatchedFmg010Records.push({
                        matchField1,
                        matchField2,
                        lessonId,
                        tid,
                        logTimestamp,
                        fmg010Record,
                        reason: 'duplicate'
                    });
                } else {
                    // 添加到匹配记录数组
                    matchedRecords.push({
                        matchField1,
                        matchField2,
                        lessonId,
                        tid,
                        logTimestamp,
                        fmg010Record,
                        fmg011Record: matchedFmg011
                    });
                    seenCombinations.add(combinationKey);

                    // 关键：从可用匹配池中移除已匹配的FMG_011记录，确保一对一匹配
                    availableFmg011Records.splice(matchedIndex, 1);
                }
            } else {
                // 记录FMG_010中无法找到匹配的项
                unmatchedFmg010Records.push({
                    matchField1,
                    matchField2,
                    lessonId,
                    tid,
                    logTimestamp,
                    fmg010Record,
                    reason: 'no_match'
                });
            }
        }
        
        // FMG_011组中剩余未被匹配的记录
        const unmatchedFmg011Records = availableFmg011Records.map(record => ({
            matchField1: record[4],
            matchField2: record[10],
            lessonId: record[3],
            tid: record[6],
            logTimestamp: record[7],
            fmg011Record: record
        }));
        
        // 数据验证
        const maxPossibleMatches = Math.min(fmg010Group.length, fmg011Group.length);
        const isValidMatch = matchedRecords.length <= maxPossibleMatches;
        
        // 输出详细统计信息
        console.log('\n--- 一对一匹配统计信息 ---');
        console.log(`原始 FMG_010 记录总数: ${fmg010Group.length}`);
        console.log(`原始 FMG_011 记录总数: ${fmg011Group.length}`);
        console.log(`理论最大匹配数: ${maxPossibleMatches}`);
        console.log(`实际匹配记录数: ${matchedRecords.length}`);
        console.log(`FMG_010 无法匹配记录数: ${unmatchedFmg010Records.length}`);
        console.log(`FMG_011 剩余未匹配记录数: ${unmatchedFmg011Records.length}`);
        console.log(`重复记录数: ${duplicateCount}`);
        console.log(`匹配率: ${((matchedRecords.length / fmg010Group.length) * 100).toFixed(2)}%`);
        console.log(`数据验证: ${isValidMatch ? '✅ 通过' : '❌ 失败'} (匹配数 ${isValidMatch ? '≤' : '>'} 理论最大值)`);
        
        // 显示FMG_010无法匹配记录的示例
        if (unmatchedFmg010Records.length > 0) {
            console.log('\n--- FMG_010 无法匹配记录示例（前5条）---');
            const sampleUnmatchedFmg010 = unmatchedFmg010Records.slice(0, 5);
            sampleUnmatchedFmg010.forEach((record, index) => {
                const reasonText = record.reason === 'duplicate' ? '重复记录' : '无匹配';
                console.log(`  ${index + 1}. 匹配字段[4]: ${record.matchField1}, [10]: ${record.matchField2}, lessonId: ${record.lessonId}, tid: ${record.tid}, log_timestamp: ${record.logTimestamp}, 原因: ${reasonText}`);
            });
            if (unmatchedFmg010Records.length > 5) {
                console.log(`  ... 还有 ${unmatchedFmg010Records.length - 5} 条无法匹配记录`);
            }
        }
        
        // 显示FMG_011剩余未匹配记录的示例
        if (unmatchedFmg011Records.length > 0) {
            console.log('\n--- FMG_011 剩余未匹配记录示例（前5条）---');
            const sampleUnmatchedFmg011 = unmatchedFmg011Records.slice(0, 5);
            sampleUnmatchedFmg011.forEach((record, index) => {
                console.log(`  ${index + 1}. 匹配字段[4]: ${record.matchField1}, [10]: ${record.matchField2}, lessonId: ${record.lessonId}, tid: ${record.tid}, log_timestamp: ${record.logTimestamp}`);
            });
            if (unmatchedFmg011Records.length > 5) {
                console.log(`  ... 还有 ${unmatchedFmg011Records.length - 5} 条剩余记录`);
            }
        }
        
        console.log('\n--- 匹配逻辑说明 ---');
        console.log('1. 一对一匹配: 每条FMG_011记录最多只能被匹配一次');
        console.log('2. 去重标准: 索引[4] + 索引[10] + log_timestamp 的组合必须唯一');
        console.log('3. 匹配条件: 索引[4] 和 索引[10] 必须完全相同');
        console.log('4. 匹配顺序: 按FMG_010记录的顺序进行匹配');
        console.log('5. 显示字段: lessonId(索引3) 和 tid(索引6) 仅用于显示，不参与匹配');
        
        // 匹配前后数据统计对比
        console.log('\n--- 匹配前后数据统计对比 ---');
        console.log(`匹配前: FMG_010(${fmg010Group.length}) + FMG_011(${fmg011Group.length}) = ${fmg010Group.length + fmg011Group.length} 条记录`);
        console.log(`匹配后: 成功匹配(${matchedRecords.length}*2) + FMG_010未匹配(${unmatchedFmg010Records.length}) + FMG_011剩余(${unmatchedFmg011Records.length}) = ${matchedRecords.length * 2 + unmatchedFmg010Records.length + unmatchedFmg011Records.length} 条记录`);

        // 正确的数据完整性验证：匹配的记录算作2条（FMG_010 + FMG_011），未匹配的各算1条
        const totalAfterMatch = matchedRecords.length * 2 + unmatchedFmg010Records.length + unmatchedFmg011Records.length;
        const totalBeforeMatch = fmg010Group.length + fmg011Group.length;
        console.log(`数据完整性验证: ${totalAfterMatch === totalBeforeMatch ? '✅ 通过' : '❌ 失败'} (${totalAfterMatch} ${totalAfterMatch === totalBeforeMatch ? '=' : '≠'} ${totalBeforeMatch})`);
        
        if (!isValidMatch) {
            console.log('\n⚠️  警告: 匹配数量超过理论最大值，请检查匹配逻辑！');
        }
        
        console.log('\n✅ 一对一匹配逻辑测试完成！');
        
    } catch (error) {
        console.error('测试一对一匹配逻辑时发生错误:', error);
    }
}

// 执行测试
testOneToOneMatching();
