/*
 * @FilePath     : /process_csv.js
 * <AUTHOR> wanghuan
 * @description  : 读取 CSV 文件并处理数据分组、匹配和分类
 * @warn         : 
 */

import fs from 'fs';
import https from 'https';

// CSV 文件路径
const csvFilePath = '/Users/<USER>/Downloads/查询当前所有update_2_0801.csv';

// 分类数组
const categorys = ['1156', '1010', '1176'];

// 延迟函数
function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// 根据 tid 获取数据的 API 函数
async function fetchByTid(tid) {
    return new Promise((resolve) => {
        const options = {
            hostname: 'tikuproxy.zuoyebang.cc',
            path: `/tikuproxy/api/v1/getbytid?tid=${tid}`,
            method: 'GET',
            headers: {
                "cookie": "sensorsdata2015session=%7B%7D; ZYBIPSCAS=IPS_06b5d1590d15f446a83ccaed407610461753928756; ZYBIPSUN=77616e676875616e3133"
            }
        };
        
        const req = https.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                try {
                    if (!data || data.trim() === '') {
                        console.log(`    tid ${tid} 返回空响应`);
                        resolve(null);
                        return;
                    }
                    const jsonData = JSON.parse(data);
                    console.log(`    tid ${tid} API 响应状态: ${res.statusCode}`);
                    resolve(jsonData);
                } catch (error) {
                    console.error(`解析 tid ${tid} 的响应数据失败:`, error);
                    resolve(null);
                }
            });
        });
        
        req.on('error', (error) => {
            console.error(`获取 tid ${tid} 数据失败:`, error);
            resolve(null);
        });
        
        req.end();
    });
}

// 简单的 CSV 解析函数
function parseCSV(csvText) {
    const lines = csvText.split('\n');
    const result = [];
    
    for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();
        if (line) {
            // 简单的 CSV 解析，处理逗号分隔
            const columns = line.split(',').map(col => col.trim().replace(/^"|"$/g, ''));
            result.push(columns);
        }
    }
    
    return result;
}

// 主处理函数
async function processCSVData() {
    try {
        console.log('=== 开始处理 CSV 数据 ===');
        console.log(`正在读取 CSV 文件: ${csvFilePath}`);
        
        // 检查文件是否存在
        if (!fs.existsSync(csvFilePath)) {
            console.error('文件不存在:', csvFilePath);
            return;
        }
        
        // 读取文件
        const csvContent = fs.readFileSync(csvFilePath, 'utf8');
        console.log(`文件大小: ${csvContent.length} 字符`);
        
        // 解析 CSV
        const csvData = parseCSV(csvContent);
        console.log(`解析完成，共 ${csvData.length} 行数据`);
        
        // 跳过表头，从第二行开始处理
        const dataRows = csvData.slice(1);
        console.log(`数据行数: ${dataRows.length}`);
        
        // 步骤1: 数据分组
        console.log('\n=== 步骤1: 数据分组 ===');
        const fmg010Group = [];
        const fmg011Group = [];
        
        dataRows.forEach((row, index) => {
            const dotName = row[5]; // 第6列，索引为5
            if (dotName === 'FMG_010') {
                fmg010Group.push(row);
            } else if (dotName === 'FMG_011') {
                fmg011Group.push(row);
            }
        });
        
        console.log(`FMG_010组: ${fmg010Group.length} 条记录`);
        console.log(`FMG_011组: ${fmg011Group.length} 条记录`);
        
        // 步骤2: 数据匹配
        console.log('\n=== 步骤2: 数据匹配 ===');
        const matchedRecords = [];
        
        for (const fmg010Record of fmg010Group) {
            const lessonId = fmg010Record[3]; // 第4列，索引为3
            const tid = fmg010Record[6]; // 第7列，索引为6
            
            // 在FMG_011组中查找匹配的记录
            const matchedFmg011 = fmg011Group.find(fmg011Record => 
                fmg011Record[3] === lessonId && fmg011Record[6] === tid
            );
            
            if (matchedFmg011) {
                matchedRecords.push({
                    lessonId,
                    tid,
                    fmg010Record,
                    fmg011Record: matchedFmg011
                });
            }
        }
        
        console.log(`找到匹配的记录: ${matchedRecords.length} 条`);
        return;
        // 步骤3和4: API调用和分类
        console.log('\n=== 步骤3-4: API调用和分类 ===');
        const matchedTypeRecords = [];
        const nonMatchedTypeRecords = [];
        
        for (let i = 0; i < matchedRecords.length; i++) {
            const record = matchedRecords[i];
            console.log(`\n处理第 ${i + 1}/${matchedRecords.length} 条记录 - tid: ${record.tid}`);
            
            try {
                const tidData = await fetchByTid(record.tid);
                
                if (tidData && tidData.data && tidData.data.category) {
                    const category = tidData.data.category.toString();
                    
                    if (categorys.includes(category)) {
                        matchedTypeRecords.push({
                            ...record,
                            tidData,
                            category
                        });
                        console.log(`  ✓ 匹配类型 - category: ${category}`);
                    } else {
                        nonMatchedTypeRecords.push({
                            ...record,
                            tidData,
                            category
                        });
                        console.log(`  ✗ 非匹配类型 - category: ${category}`);
                    }
                } else {
                    console.log(`  ! API调用失败或数据格式错误`);
                    nonMatchedTypeRecords.push({
                        ...record,
                        tidData: null,
                        category: null
                    });
                }
                
                // 添加延迟避免请求过于频繁
                await delay(500);
                
            } catch (error) {
                console.error(`  处理 tid ${record.tid} 时发生错误:`, error);
                nonMatchedTypeRecords.push({
                    ...record,
                    tidData: null,
                    category: null,
                    error: error.message
                });
            }
        }
        
        // 步骤5: 输出结果
        console.log('\n=== 步骤5: 输出结果 ===');
        console.log(`\n匹配类型记录数量: ${matchedTypeRecords.length}`);
        console.log(`非匹配类型记录数量: ${nonMatchedTypeRecords.length}`);
        
        console.log('\n=== 匹配类型记录详情 ===');
        matchedTypeRecords.forEach((record, index) => {
            console.log(`${index + 1}. lessonId: ${record.lessonId}, tid: ${record.tid}, category: ${record.category}`);
        });
        
        console.log('\n=== 非匹配类型记录详情 ===');
        nonMatchedTypeRecords.forEach((record, index) => {
            console.log(`${index + 1}. lessonId: ${record.lessonId}, tid: ${record.tid}, category: ${record.category || 'N/A'}`);
        });
        
        // 保存结果到文件
        const results = {
            summary: {
                totalMatched: matchedRecords.length,
                matchedType: matchedTypeRecords.length,
                nonMatchedType: nonMatchedTypeRecords.length
            },
            matchedTypeRecords,
            nonMatchedTypeRecords
        };
        
        const outputFile = 'csv_processing_results.json';
        fs.writeFileSync(outputFile, JSON.stringify(results, null, 2));
        console.log(`\n结果已保存到: ${outputFile}`);
        
    } catch (error) {
        console.error('处理 CSV 数据时发生错误:', error);
    }
}

// 执行主函数
processCSVData();
