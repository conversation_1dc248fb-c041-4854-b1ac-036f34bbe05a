/*
 * @FilePath     : /process_csv.js
 * <AUTHOR> wanghuan
 * @description  : 读取 CSV 文件并处理数据分组、匹配和分类
 * @warn         : 
 */

import fs from 'fs';
import https from 'https';

// CSV 文件路径
const csvFilePath = '/Users/<USER>/Downloads/查询当前所有update_2_0801.csv';

// 分类数组
const categorys = ['1156', '1010', '1176'];

// 延迟函数
function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// 根据 tid 获取数据的 API 函数
async function fetchByTid(tid) {
    return new Promise((resolve) => {
        const options = {
            hostname: 'tikuproxy.zuoyebang.cc',
            path: `/tikuproxy/api/v1/getbytid?tid=${tid}`,
            method: 'GET',
            headers: {
                "cookie": "sensorsdata2015session=%7B%7D; ZYBIPSCAS=IPS_06b5d1590d15f446a83ccaed407610461753928756; ZYBIPSUN=77616e676875616e3133"
            }
        };
        
        const req = https.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                try {
                    if (!data || data.trim() === '') {
                        console.log(`    tid ${tid} 返回空响应`);
                        resolve(null);
                        return;
                    }
                    const jsonData = JSON.parse(data);
                    console.log(`    tid ${tid} API 响应状态: ${res.statusCode}`);
                    resolve(jsonData);
                } catch (error) {
                    console.error(`解析 tid ${tid} 的响应数据失败:`, error);
                    resolve(null);
                }
            });
        });
        
        req.on('error', (error) => {
            console.error(`获取 tid ${tid} 数据失败:`, error);
            resolve(null);
        });
        
        req.end();
    });
}

// 简单的 CSV 解析函数
function parseCSV(csvText) {
    const lines = csvText.split('\n');
    const result = [];
    
    for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();
        if (line) {
            // 简单的 CSV 解析，处理逗号分隔
            const columns = line.split(',').map(col => col.trim().replace(/^"|"$/g, ''));
            result.push(columns);
        }
    }
    
    return result;
}

// 主处理函数
async function processCSVData() {
    try {
        console.log('=== 开始处理 CSV 数据 ===');
        console.log(`正在读取 CSV 文件: ${csvFilePath}`);
        
        // 检查文件是否存在
        if (!fs.existsSync(csvFilePath)) {
            console.error('文件不存在:', csvFilePath);
            return;
        }
        
        // 读取文件
        const csvContent = fs.readFileSync(csvFilePath, 'utf8');
        console.log(`文件大小: ${csvContent.length} 字符`);
        
        // 解析 CSV
        const csvData = parseCSV(csvContent);
        console.log(`解析完成，共 ${csvData.length} 行数据`);
        
        // 跳过表头，从第二行开始处理
        const dataRows = csvData.slice(1);
        console.log(`数据行数: ${dataRows.length}`);
        
        // 步骤1: 数据分组
        console.log('\n=== 步骤1: 数据分组 ===');
        const fmg010Group = [];
        const fmg011Group = [];
        
        dataRows.forEach((row, index) => {
            const dotName = row[5]; // 第6列，索引为5
            if (dotName === 'FMG_010') {
                fmg010Group.push(row);
            } else if (dotName === 'FMG_011') {
                fmg011Group.push(row);
            }
        });
        
        console.log(`FMG_010组: ${fmg010Group.length} 条记录`);
        console.log(`FMG_011组: ${fmg011Group.length} 条记录`);
        
        // 步骤2: 数据匹配（一对一匹配机制）
        console.log('\n=== 步骤2: 数据匹配（一对一匹配机制）===');
        const matchedRecords = [];
        const unmatchedFmg010Records = []; // FMG_010中无法找到匹配的记录
        const seenCombinations = new Set(); // 用于去重的集合
        let duplicateCount = 0; // 重复记录计数

        // 创建FMG_011组的副本用于匹配操作，避免修改原数组
        const availableFmg011Records = [...fmg011Group];

        console.log('\n--- 开始一对一匹配 ---');
        console.log(`FMG_010组记录数: ${fmg010Group.length}`);
        console.log(`FMG_011组记录数: ${fmg011Group.length}`);
        console.log(`理论最大匹配数: ${Math.min(fmg010Group.length, fmg011Group.length)}`);

        for (const fmg010Record of fmg010Group) {
            const lessonId = fmg010Record[3]; // 第4列，索引为3
            console.log("%c Line:142 🍯 lessonId", "color:#4fff4B", lessonId);
            const tid = fmg010Record[6]; // 第7列，索引为6
            console.log("%c Line:144 🥑 tid", "color:#6ec1c2", tid);
            const logTimestamp = fmg010Record[7]; // 第8列，索引为7 (log_timestamp)
            const combinationKey = `${lessonId}_${tid}_${logTimestamp}`; // 创建唯一标识符，包含时间戳

            // 在可用的FMG_011记录中查找匹配的记录
            const matchedIndex = availableFmg011Records.findIndex(fmg011Record =>
                fmg011Record[3] === lessonId && fmg011Record[6] === tid
            );

            if (matchedIndex !== -1) {
                const matchedFmg011 = availableFmg011Records[matchedIndex];

                // 检查是否已存在相同的 lessonId、tid 和 log_timestamp 组合
                if (seenCombinations.has(combinationKey)) {
                    duplicateCount++;
                    console.log(`  发现重复记录: lessonId=${lessonId}, tid=${tid}, log_timestamp=${logTimestamp}`);
                    // 重复记录也算作无法匹配
                    unmatchedFmg010Records.push({
                        lessonId,
                        tid,
                        logTimestamp,
                        fmg010Record,
                        reason: 'duplicate'
                    });
                } else {
                    // 添加到匹配记录数组
                    matchedRecords.push({
                        lessonId,
                        tid,
                        logTimestamp,
                        fmg010Record,
                        fmg011Record: matchedFmg011
                    });
                    seenCombinations.add(combinationKey);

                    // 关键：从可用匹配池中移除已匹配的FMG_011记录，确保一对一匹配
                    availableFmg011Records.splice(matchedIndex, 1);
                }
            } else {
                // 记录FMG_010中无法找到匹配的项
                unmatchedFmg010Records.push({
                    lessonId,
                    tid,
                    logTimestamp,
                    fmg010Record,
                    reason: 'no_match'
                });
            }
        }

        // FMG_011组中剩余未被匹配的记录
        const unmatchedFmg011Records = availableFmg011Records.map(record => ({
            lessonId: record[3],
            tid: record[6],
            logTimestamp: record[7],
            fmg011Record: record
        }));

        // 数据验证
        const maxPossibleMatches = Math.min(fmg010Group.length, fmg011Group.length);
        const isValidMatch = matchedRecords.length <= maxPossibleMatches;

        // 输出详细统计信息
        console.log('\n--- 一对一匹配统计信息 ---');
        console.log(`原始 FMG_010 记录总数: ${fmg010Group.length}`);
        console.log(`原始 FMG_011 记录总数: ${fmg011Group.length}`);
        console.log(`理论最大匹配数: ${maxPossibleMatches}`);
        console.log(`实际匹配记录数: ${matchedRecords.length}`);
        console.log(`FMG_010 无法匹配记录数: ${unmatchedFmg010Records.length}`);
        console.log(`FMG_011 剩余未匹配记录数: ${unmatchedFmg011Records.length}`);
        console.log(`重复记录数: ${duplicateCount}`);
        console.log(`匹配率: ${((matchedRecords.length / fmg010Group.length) * 100).toFixed(2)}%`);
        console.log(`数据验证: ${isValidMatch ? '✅ 通过' : '❌ 失败'} (匹配数 ${isValidMatch ? '≤' : '>'} 理论最大值)`);

        // 显示FMG_010无法匹配记录的示例
        if (unmatchedFmg010Records.length > 0) {
            console.log('\n--- FMG_010 无法匹配记录示例（前5条）---');
            const sampleUnmatchedFmg010 = unmatchedFmg010Records.slice(0, 5);
            sampleUnmatchedFmg010.forEach((record, index) => {
                const reasonText = record.reason === 'duplicate' ? '重复记录' : '无匹配';
                console.log(`  ${index + 1}. lessonId: ${record.lessonId}, tid: ${record.tid}, log_timestamp: ${record.logTimestamp}, 原因: ${reasonText}`);
            });
            if (unmatchedFmg010Records.length > 5) {
                console.log(`  ... 还有 ${unmatchedFmg010Records.length - 5} 条无法匹配记录`);
            }
        }

        // 显示FMG_011剩余未匹配记录的示例
        if (unmatchedFmg011Records.length > 0) {
            console.log('\n--- FMG_011 剩余未匹配记录示例（前5条）---');
            const sampleUnmatchedFmg011 = unmatchedFmg011Records.slice(0, 5);
            sampleUnmatchedFmg011.forEach((record, index) => {
                console.log(`  ${index + 1}. lessonId: ${record.lessonId}, tid: ${record.tid}, log_timestamp: ${record.logTimestamp}`);
            });
            if (unmatchedFmg011Records.length > 5) {
                console.log(`  ... 还有 ${unmatchedFmg011Records.length - 5} 条剩余记录`);
            }
        }

        console.log('\n--- 匹配逻辑说明 ---');
        console.log('1. 一对一匹配: 每条FMG_011记录最多只能被匹配一次');
        console.log('2. 去重标准: lessonId + tid + log_timestamp 的组合必须唯一');
        console.log('3. 匹配条件: lessonId 和 tid 必须完全相同');
        console.log('4. 匹配顺序: 按FMG_010记录的顺序进行匹配');

        // 匹配前后数据统计对比
        console.log('\n--- 匹配前后数据统计对比 ---');
        console.log(`匹配前: FMG_010(${fmg010Group.length}) + FMG_011(${fmg011Group.length}) = ${fmg010Group.length + fmg011Group.length} 条记录`);
        console.log(`匹配后: 成功匹配(${matchedRecords.length}*2) + FMG_010未匹配(${unmatchedFmg010Records.length}) + FMG_011剩余(${unmatchedFmg011Records.length}) = ${matchedRecords.length * 2 + unmatchedFmg010Records.length + unmatchedFmg011Records.length} 条记录`);

        // 正确的数据完整性验证：匹配的记录算作2条（FMG_010 + FMG_011），未匹配的各算1条
        const totalAfterMatch = matchedRecords.length * 2 + unmatchedFmg010Records.length + unmatchedFmg011Records.length;
        const totalBeforeMatch = fmg010Group.length + fmg011Group.length;
        console.log(`数据完整性验证: ${totalAfterMatch === totalBeforeMatch ? '✅ 通过' : '❌ 失败'} (${totalAfterMatch} ${totalAfterMatch === totalBeforeMatch ? '=' : '≠'} ${totalBeforeMatch})`);

        if (!isValidMatch) {
            console.log('\n⚠️  警告: 匹配数量超过理论最大值，请检查匹配逻辑！');
        }

        // 步骤3和4: API调用和分类
        console.log('\n=== 步骤3-4: API调用和分类 ===');
        const matchedTypeRecords = [];
        const nonMatchedTypeRecords = [];
        
        for (let i = 0; i < matchedRecords.length; i++) {
            const record = matchedRecords[i];
            console.log(`\n处理第 ${i + 1}/${matchedRecords.length} 条记录 - tid: ${record.tid}`);
            
            try {
                const tidData = await fetchByTid(record.tid);
                
                if (tidData && tidData.data && tidData.data.category) {
                    const category = tidData.data.category.toString();
                    
                    if (categorys.includes(category)) {
                        matchedTypeRecords.push({
                            ...record,
                            tidData,
                            category
                        });
                        console.log(`  ✓ 匹配类型 - category: ${category}`);
                    } else {
                        nonMatchedTypeRecords.push({
                            ...record,
                            tidData,
                            category
                        });
                        console.log(`  ✗ 非匹配类型 - category: ${category}`);
                    }
                } else {
                    console.log(`  ! API调用失败或数据格式错误`);
                    nonMatchedTypeRecords.push({
                        ...record,
                        tidData: null,
                        category: null
                    });
                }
                
                // 添加延迟避免请求过于频繁
                await delay(100);
                
            } catch (error) {
                console.error(`  处理 tid ${record.tid} 时发生错误:`, error);
                nonMatchedTypeRecords.push({
                    ...record,
                    tidData: null,
                    category: null,
                    error: error.message
                });
            }
        }
        
        // 步骤5: 输出结果
        console.log('\n=== 步骤5: 输出结果 ===');
        console.log(`\n匹配类型记录数量: ${matchedTypeRecords.length}`);
        console.log(`非匹配类型记录数量: ${nonMatchedTypeRecords.length}`);
        
        console.log('\n=== 匹配类型记录详情 ===');
        matchedTypeRecords.forEach((record, index) => {
            console.log(`${index + 1}. lessonId: ${record.lessonId}, tid: ${record.tid}, category: ${record.category}`);
        });
        
        console.log('\n=== 非匹配类型记录详情 ===');
        nonMatchedTypeRecords.forEach((record, index) => {
            console.log(`${index + 1}. lessonId: ${record.lessonId}, tid: ${record.tid}, category: ${record.category || 'N/A'}`);
        });
        
        // 保存结果到文件
        const results = {
            summary: {
                totalMatched: matchedRecords.length,
                matchedType: matchedTypeRecords.length,
                nonMatchedType: nonMatchedTypeRecords.length
            },
            matchedTypeRecords,
            nonMatchedTypeRecords
        };
        
        const outputFile = 'csv_processing_results.json';
        fs.writeFileSync(outputFile, JSON.stringify(results, null, 2));
        console.log(`\n结果已保存到: ${outputFile}`);
        
    } catch (error) {
        console.error('处理 CSV 数据时发生错误:', error);
    }
}

// 执行主函数
processCSVData();
